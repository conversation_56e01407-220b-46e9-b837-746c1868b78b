/**
 * Internationalization Utilities
 * 
 * Provides text translations and language-specific formatting
 * for the Insurance Portal application.
 * 
 * Features:
 * - Text translation mappings
 * - Language-specific date/currency formatting
 * - Fallback handling for missing translations
 */

import { SUPPORTED_LANGUAGES } from '../stores/languageStore.js';

// Text translations for PolicyList page
export const translations = {
  // Page titles and headers
  pageTitle: {
    [SUPPORTED_LANGUAGES.TH]: 'กรมธรรม์ประกันภัย',
    [SUPPORTED_LANGUAGES.EN]: 'Insurance Policies'
  },
  
  // Member selection
  selectMember: {
    [SUPPORTED_LANGUAGES.TH]: 'เลือกสมาชิก',
    [SUPPORTED_LANGUAGES.EN]: 'Select Member'
  },
  selectInsurer: {
    [SUPPORTED_LANGUAGES.TH]: 'เลือกบริษัทประกัน',
    [SUPPORTED_LANGUAGES.EN]: 'Select Insurer'
  },
  noInsurerSelected: {
    [SUPPORTED_LANGUAGES.TH]: 'ไม่ได้เลือกบริษัทประกัน',
    [SUPPORTED_LANGUAGES.EN]: 'No insurer selected'
  },
  noInsurerFound: {
    [SUPPORTED_LANGUAGES.TH]: 'ไม่พบบริษัทประกัน',
    [SUPPORTED_LANGUAGES.EN]: 'No insurers found'
  },
  
  // Policy information
  policiesOf: {
    [SUPPORTED_LANGUAGES.TH]: 'กรมธรรม์ของ',
    [SUPPORTED_LANGUAGES.EN]: 'Policies of'
  },
  memberCode: {
    [SUPPORTED_LANGUAGES.TH]: 'รหัสสมาชิก',
    [SUPPORTED_LANGUAGES.EN]: 'Member Code'
  },
  
  // Policy details
  policyNumber: {
    [SUPPORTED_LANGUAGES.TH]: 'เลขที่กรมธรรม์',
    [SUPPORTED_LANGUAGES.EN]: 'Policy Number'
  },
  certificateNumber: {
    [SUPPORTED_LANGUAGES.TH]: 'เลขที่ใบรับรอง',
    [SUPPORTED_LANGUAGES.EN]: 'Certificate Number'
  },
  insuranceCardNumber: {
    [SUPPORTED_LANGUAGES.TH]: 'เลขที่บัตรประกัน',
    [SUPPORTED_LANGUAGES.EN]: 'Insurance Card Number'
  },
  staffNumber: {
    [SUPPORTED_LANGUAGES.TH]: 'รหัสพนักงาน',
    [SUPPORTED_LANGUAGES.EN]: 'Staff Number'
  },
  coverageAmount: {
    [SUPPORTED_LANGUAGES.TH]: 'วงเงินคุ้มครอง',
    [SUPPORTED_LANGUAGES.EN]: 'Coverage Amount'
  },
  premium: {
    [SUPPORTED_LANGUAGES.TH]: 'เบี้ยประกัน',
    [SUPPORTED_LANGUAGES.EN]: 'Premium'
  },
  effectiveDate: {
    [SUPPORTED_LANGUAGES.TH]: 'วันที่มีผล',
    [SUPPORTED_LANGUAGES.EN]: 'Effective Date'
  },
  expiryDate: {
    [SUPPORTED_LANGUAGES.TH]: 'วันที่หมดอายุ',
    [SUPPORTED_LANGUAGES.EN]: 'Expiry Date'
  },
  planCode: {
    [SUPPORTED_LANGUAGES.TH]: 'รหัสแผน',
    [SUPPORTED_LANGUAGES.EN]: 'Plan Code'
  },
  
  // Member information
  memberInfo: {
    [SUPPORTED_LANGUAGES.TH]: 'ข้อมูลสมาชิก',
    [SUPPORTED_LANGUAGES.EN]: 'Member Information'
  },
  citizenId: {
    [SUPPORTED_LANGUAGES.TH]: 'เลขบัตรประชาชน',
    [SUPPORTED_LANGUAGES.EN]: 'Citizen ID'
  },
  memberType: {
    [SUPPORTED_LANGUAGES.TH]: 'ประเภทสมาชิก',
    [SUPPORTED_LANGUAGES.EN]: 'Member Type'
  },
  cardType: {
    [SUPPORTED_LANGUAGES.TH]: 'ประเภทบัตร',
    [SUPPORTED_LANGUAGES.EN]: 'Card Type'
  },
  language: {
    [SUPPORTED_LANGUAGES.TH]: 'ภาษา',
    [SUPPORTED_LANGUAGES.EN]: 'Language'
  },
  citizenship: {
    [SUPPORTED_LANGUAGES.TH]: 'สัญชาติ',
    [SUPPORTED_LANGUAGES.EN]: 'Citizenship'
  },
  
  // Company information
  companyInfo: {
    [SUPPORTED_LANGUAGES.TH]: 'ข้อมูลบริษัท',
    [SUPPORTED_LANGUAGES.EN]: 'Company Information'
  },
  insurer: {
    [SUPPORTED_LANGUAGES.TH]: 'บริษัทประกัน',
    [SUPPORTED_LANGUAGES.EN]: 'Insurer'
  },
  companyName: {
    [SUPPORTED_LANGUAGES.TH]: 'ชื่อบริษัท',
    [SUPPORTED_LANGUAGES.EN]: 'Company Name'
  },
  
  // Loading and error states
  loading: {
    [SUPPORTED_LANGUAGES.TH]: 'กำลังโหลด...',
    [SUPPORTED_LANGUAGES.EN]: 'Loading...'
  },
  loadingPolicies: {
    [SUPPORTED_LANGUAGES.TH]: 'กำลังโหลดข้อมูลกรมธรรม์...',
    [SUPPORTED_LANGUAGES.EN]: 'Loading policies...'
  },
  noDataAvailable: {
    [SUPPORTED_LANGUAGES.TH]: 'ไม่มีข้อมูล',
    [SUPPORTED_LANGUAGES.EN]: 'No data available'
  },
  errorLoadingData: {
    [SUPPORTED_LANGUAGES.TH]: 'เกิดข้อผิดพลาดในการโหลดข้อมูล',
    [SUPPORTED_LANGUAGES.EN]: 'Error loading data'
  },
  retry: {
    [SUPPORTED_LANGUAGES.TH]: 'ลองใหม่',
    [SUPPORTED_LANGUAGES.EN]: 'Retry'
  },
  
  // Selection prompts
  selectMemberFirst: {
    [SUPPORTED_LANGUAGES.TH]: 'กรุณาเลือกสมาชิกก่อนดูข้อมูลกรมธรรม์',
    [SUPPORTED_LANGUAGES.EN]: 'Please select a member to view policies'
  },
  incompleteMemberData: {
    [SUPPORTED_LANGUAGES.TH]: 'ข้อมูลสมาชิกไม่ครบถ้วน',
    [SUPPORTED_LANGUAGES.EN]: 'Incomplete member data'
  },
  selectBothCitizenAndInsurer: {
    [SUPPORTED_LANGUAGES.TH]: 'กรุณาเลือกทั้งสมาชิกและบริษัทประกัน',
    [SUPPORTED_LANGUAGES.EN]: 'Please select both citizen and insurer'
  },
  
  // Policy types (for display)
  policyTypes: {
    Auto: {
      [SUPPORTED_LANGUAGES.TH]: 'ประกันรถยนต์',
      [SUPPORTED_LANGUAGES.EN]: 'Auto Insurance'
    },
    Home: {
      [SUPPORTED_LANGUAGES.TH]: 'ประกันบ้าน',
      [SUPPORTED_LANGUAGES.EN]: 'Home Insurance'
    },
    Life: {
      [SUPPORTED_LANGUAGES.TH]: 'ประกันชีวิต',
      [SUPPORTED_LANGUAGES.EN]: 'Life Insurance'
    },
    Health: {
      [SUPPORTED_LANGUAGES.TH]: 'ประกันสุขภาพ',
      [SUPPORTED_LANGUAGES.EN]: 'Health Insurance'
    },
    Medical: {
      [SUPPORTED_LANGUAGES.TH]: 'ประกันการรักษาพยาบาล',
      [SUPPORTED_LANGUAGES.EN]: 'Medical Insurance'
    },
    Business: {
      [SUPPORTED_LANGUAGES.TH]: 'ประกันธุรกิจ',
      [SUPPORTED_LANGUAGES.EN]: 'Business Insurance'
    },
    General: {
      [SUPPORTED_LANGUAGES.TH]: 'ประกันทั่วไป',
      [SUPPORTED_LANGUAGES.EN]: 'General Insurance'
    },
    Basic: {
      [SUPPORTED_LANGUAGES.TH]: 'แผนพื้นฐาน',
      [SUPPORTED_LANGUAGES.EN]: 'Basic Plan'
    },
    Premium: {
      [SUPPORTED_LANGUAGES.TH]: 'แผนพรีเมียม',
      [SUPPORTED_LANGUAGES.EN]: 'Premium Plan'
    },
    Executive: {
      [SUPPORTED_LANGUAGES.TH]: 'แผนผู้บริหาร',
      [SUPPORTED_LANGUAGES.EN]: 'Executive Plan'
    }
  }
};

/**
 * Get translated text
 * @param {string} key - Translation key
 * @param {string} language - Language code
 * @param {string} fallback - Fallback text if translation not found
 * @returns {string} Translated text
 */
export function t(key, language = SUPPORTED_LANGUAGES.TH, fallback = '') {
  try {
    const translation = translations[key];
    if (!translation) {
      console.warn(`Translation key not found: ${key}`);
      return fallback || key;
    }
    
    return translation[language] || translation[SUPPORTED_LANGUAGES.TH] || fallback || key;
  } catch (error) {
    console.warn(`Error getting translation for key: ${key}`, error);
    return fallback || key;
  }
}

/**
 * Get policy type display name
 * @param {string} policyType - Policy type key
 * @param {string} language - Language code
 * @returns {string} Localized policy type name
 */
export function getPolicyTypeTranslation(policyType, language = SUPPORTED_LANGUAGES.TH) {
  if (!policyType) return '';
  
  const policyTranslations = translations.policyTypes[policyType];
  if (!policyTranslations) {
    return policyType; // Return original if no translation found
  }
  
  return policyTranslations[language] || policyTranslations[SUPPORTED_LANGUAGES.TH] || policyType;
}

/**
 * Format currency based on language
 * @param {number} amount - Amount to format
 * @param {string} language - Language code
 * @returns {string} Formatted currency
 */
export function formatCurrencyByLanguage(amount, language = SUPPORTED_LANGUAGES.TH) {
  if (isNaN(amount)) return language === SUPPORTED_LANGUAGES.EN ? 'N/A' : 'ไม่มีข้อมูล';
  
  const locale = language === SUPPORTED_LANGUAGES.EN ? 'en-US' : 'th-TH';
  const currency = 'THB';
  
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(amount);
}

/**
 * Format date based on language
 * @param {string|Date} date - Date to format
 * @param {string} language - Language code
 * @returns {string} Formatted date
 */
export function formatDateByLanguage(date, language = SUPPORTED_LANGUAGES.TH) {
  if (!date) return language === SUPPORTED_LANGUAGES.EN ? 'N/A' : 'ไม่มีข้อมูล';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (isNaN(dateObj.getTime())) {
      return language === SUPPORTED_LANGUAGES.EN ? 'Invalid date' : 'วันที่ไม่ถูกต้อง';
    }
    
    const locale = language === SUPPORTED_LANGUAGES.EN ? 'en-US' : 'th-TH';
    const options = {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    };
    
    return dateObj.toLocaleDateString(locale, options);
  } catch (error) {
    console.warn('Error formatting date:', error);
    return language === SUPPORTED_LANGUAGES.EN ? 'Invalid date' : 'วันที่ไม่ถูกต้อง';
  }
}
